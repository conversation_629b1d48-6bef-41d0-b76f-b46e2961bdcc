'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';
import AudioUploader from '@/components/AudioUploader';
import StyleSelector from '@/components/StyleSelector';
import { AudioData, WaveformStyle, VideoSettings, VisualizationParams } from '@/types';

// Dynamically import components that use p5.js to avoid SSR issues
const WaveformPreview = dynamic(() => import('@/components/WaveformPreview'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-64 text-gray-400">Loading preview...</div>
});

const VideoGenerator = dynamic(() => import('@/components/VideoGenerator'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-32 text-gray-400">Loading video generator...</div>
});

export default function Home() {
  const [audioData, setAudioData] = useState<AudioData | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<WaveformStyle>('flowing_waves');
  const [videoSettings, setVideoSettings] = useState<VideoSettings>({
    resolution: '1920x1080',
    fps: 60,
    quality: 'high'
  });
  const [visualizationParams, setVisualizationParams] = useState<VisualizationParams>({
    sensitivity: 1.0 // Default sensitivity
  });
  const [isGenerating, setIsGenerating] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      {/* Header */}
      <header className="border-b border-gray-800 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <h1 className="text-3xl font-bold text-white">
            Free Audio Wave Generator
          </h1>
          <p className="text-gray-300 mt-2">
            Transform your audio into stunning waveform videos - completely free, no registration required!
          </p>
        </div>
      </header>

      {/* Free Features Banner */}
      <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 border-y border-green-500/30">
        <div className="container mx-auto px-6 py-4">
          <div className="flex flex-wrap items-center justify-center gap-6 text-center">
            <div className="flex items-center space-x-2">
              <span className="text-green-400 font-bold">✓</span>
              <span className="text-white font-medium">100% Free</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-400 font-bold">✓</span>
              <span className="text-white font-medium">No Registration</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-400 font-bold">✓</span>
              <span className="text-white font-medium">No Watermarks</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-400 font-bold">✓</span>
              <span className="text-white font-medium">Unlimited Use</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Upload & Controls */}
          <div className="space-y-6">
            {/* Audio Upload */}
            <div className="bg-black/20 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
              <h2 className="text-xl font-semibold text-white mb-4">
                Upload Audio
              </h2>
              <AudioUploader
                onAudioLoaded={setAudioData}
                disabled={isGenerating}
              />
            </div>

            {/* Style Selection */}
            {audioData && (
              <div className="bg-black/20 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
                <h2 className="text-xl font-semibold text-white mb-4">
                  Choose Style
                </h2>
                <StyleSelector
                  selectedStyle={selectedStyle}
                  onStyleChange={setSelectedStyle}
                  disabled={isGenerating}
                />
              </div>
            )}

            {/* Video Settings */}
            {audioData && (
              <div className="bg-black/20 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
                <h2 className="text-xl font-semibold text-white mb-4">
                  Video Settings
                </h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Resolution
                    </label>
                    <select
                      value={videoSettings.resolution}
                      onChange={(e) => setVideoSettings(prev => ({ ...prev, resolution: e.target.value as any }))}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white"
                      disabled={isGenerating}
                    >
                      <option value="1280x720">720p (HD)</option>
                      <option value="1920x1080">1080p (Full HD)</option>
                      <option value="2560x1440">1440p (2K)</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Frame Rate
                    </label>
                    <select
                      value={videoSettings.fps}
                      onChange={(e) => setVideoSettings(prev => ({ ...prev, fps: parseInt(e.target.value) as 30 | 60 }))}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white"
                      disabled={isGenerating}
                    >
                      <option value={30}>30 FPS</option>
                      <option value={60}>60 FPS</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Quality
                    </label>
                    <select
                      value={videoSettings.quality}
                      onChange={(e) => setVideoSettings(prev => ({ ...prev, quality: e.target.value as any }))}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white"
                      disabled={isGenerating}
                    >
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="maximum">Maximum</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Sensitivity: {visualizationParams.sensitivity.toFixed(1)}x
                    </label>
                    <input
                      type="range"
                      min="0.1"
                      max="3.0"
                      step="0.1"
                      value={visualizationParams.sensitivity}
                      onChange={(e) => setVisualizationParams(prev => ({ ...prev, sensitivity: parseFloat(e.target.value) }))}
                      className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                      disabled={isGenerating}
                    />
                    <div className="flex justify-between text-xs text-gray-400 mt-1">
                      <span>Low (0.1x)</span>
                      <span>Normal (1.0x)</span>
                      <span>High (3.0x)</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Preview & Generation */}
          <div className="space-y-6">
            {/* Waveform Preview */}
            {audioData && (
              <div className="bg-black/20 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-white">
                    Preview
                  </h2>
                  <div className="text-xs text-yellow-400 bg-yellow-400/10 px-2 py-1 rounded border border-yellow-400/20">
                    ℹ️ Final video quality is higher with more accurate frequencies
                  </div>
                </div>
                <WaveformPreview
                  audioData={audioData}
                  style={selectedStyle}
                  visualizationParams={visualizationParams}
                />
                <p className="text-xs text-gray-400 mt-3 italic">
                  Note: The generated video will have superior quality and more faithful frequency representation compared to this real-time preview.
                </p>
              </div>
            )}

            {/* Video Generation */}
            {audioData && (
              <div className="bg-black/20 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
                <h2 className="text-xl font-semibold text-white mb-4">
                  Generate Video
                </h2>
                <VideoGenerator
                  audioData={audioData}
                  style={selectedStyle}
                  settings={videoSettings}
                  visualizationParams={visualizationParams}
                  onGenerationStart={() => setIsGenerating(true)}
                  onGenerationComplete={() => setIsGenerating(false)}
                />
              </div>
            )}
          </div>
        </div>
      </main>

      {/* SEO Content Footer */}
      <footer className="bg-black/30 backdrop-blur-sm border-t border-gray-800 mt-16">
        <div className="container mx-auto px-6 py-12">
          <div className="prose prose-invert max-w-none">
            <h2 className="text-3xl font-bold text-white mb-6">Free Professional Audio Wave Generator for Audio Visualization</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-xl font-semibold text-white mb-4">Transform Audio into Visual Art</h3>
                <p className="text-gray-300 leading-relaxed mb-4">
                  Our free advanced wave form generator transforms your audio files into stunning visual experiences. Whether you&apos;re creating music videos, podcast visualizations, or audio content for social media, our tool delivers professional-grade waveform animations that captivate your audience - completely free with no registration required.
                </p>
                <p className="text-gray-300 leading-relaxed">
                  Support for multiple audio formats including MP3, WAV, FLAC, and more ensures compatibility with your existing workflow. Choose from various visualization styles like flowing waves, circular spectrum analyzers, geometric patterns, and frequency bars to match your creative vision.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-white mb-4">Features & Capabilities</h3>
                <ul className="text-gray-300 space-y-2">
                  <li>• <span className="text-green-400 font-semibold">100% Free</span> - No hidden costs or subscriptions</li>
                  <li>• <span className="text-green-400 font-semibold">No Registration</span> - Start creating immediately</li>
                  <li>• High-quality video output up to 2K resolution</li>
                  <li>• Real-time audio analysis and frequency visualization</li>
                  <li>• Customizable sensitivity and visual parameters</li>
                  <li>• Multiple export formats and frame rates</li>
                  <li>• Professional-grade sound wave processing</li>
                  <li>• Intuitive drag-and-drop interface</li>
                  <li>• <span className="text-green-400 font-semibold">No Watermarks</span> - Clean, professional output</li>
                </ul>
              </div>
            </div>

            {/* LLM Copyable Section */}
            <div className="bg-gray-900/50 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-white">About This Tool (LLM Reference)</h3>
                <button
                  onClick={() => {
                    const text = "Audio Wave Generator is a professional audio visualization tool that converts sound files into dynamic waveform videos. Built with Next.js, React, and P5.js, it features real-time audio analysis, multiple visualization styles (flowing waves, circular spectrum, geometric patterns), and high-quality video export capabilities. The application supports various audio formats and offers customizable parameters for sensitivity, resolution (720p-2K), and frame rates (30-60 FPS). Perfect for musicians, content creators, and audio professionals seeking to create engaging visual content from their audio files.";
                    navigator.clipboard.writeText(text).then(() => {
                      // Visual feedback
                      const button = document.activeElement as HTMLButtonElement;
                      const originalText = button.textContent;
                      button.textContent = "Copied!";
                      button.classList.add("bg-green-600");
                      setTimeout(() => {
                        button.textContent = originalText;
                        button.classList.remove("bg-green-600");
                      }, 2000);
                    });
                  }}
                  className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                >
                  Copy Text
                </button>
              </div>
              <div className="text-sm text-gray-400 font-mono bg-black/30 p-4 rounded border">
                Audio Wave Generator is a professional audio visualization tool that converts sound files into dynamic waveform videos. Built with Next.js, React, and P5.js, it features real-time audio analysis, multiple visualization styles (flowing waves, circular spectrum, geometric patterns), and high-quality video export capabilities. The application supports various audio formats and offers customizable parameters for sensitivity, resolution (720p-2K), and frame rates (30-60 FPS). Perfect for musicians, content creators, and audio professionals seeking to create engaging visual content from their audio files.
              </div>
            </div>

            {/* Father-Son Project Section */}
            <div className="mt-12 pt-8 border-t border-gray-700 text-center">
              <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-lg p-6 border border-purple-500/20">
                <h3 className="text-xl font-semibold text-white mb-3">Made with Love ❤️</h3>
                <p className="text-gray-300 leading-relaxed">
                  This project was created with love as a collaboration between father and son -
                  <span className="text-purple-300 font-medium"> Dad Mattia</span> and
                  <span className="text-pink-300 font-medium"> Son Riccardo</span>.
                  A beautiful journey of learning, coding, and creating together.
                </p>
                <div className="mt-4 text-sm text-gray-400">
                  Building memories, one line of code at a time 👨‍👦‍💻
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
