export default function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Audio Wave Generator",
    "description": "Transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes.",
    "url": "https://www.audiowavegenerator.com",
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "creator": {
      "@type": "Person",
      "name": "Mattia & Riccardo"
    },
    "featureList": [
      "Audio to video conversion",
      "Multiple waveform styles",
      "High-quality video export",
      "Real-time audio analysis",
      "Customizable parameters",
      "Professional visualization"
    ],
    "screenshot": "https://www.audiowavegenerator.com/screenshot.png",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
