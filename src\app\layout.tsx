import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import { Analytics } from "@vercel/analytics/next";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Audio Wave Generator - Professional Waveform Video Creator",
  description: "Transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes.",
  keywords: ["audio wave generator", "wave form generator", "audio visualization", "waveform", "video generator", "music visualization", "audio to video", "sound visualization", "audio waveform", "sound wave generator"],
  authors: [{ name: "Audio Wave Generator" }],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
        <Analytics />
      </body>
    </html>
  );
}
