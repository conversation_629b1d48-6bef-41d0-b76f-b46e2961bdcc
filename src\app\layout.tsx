import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Analytics } from "@vercel/analytics/next";
import StructuredData from "@/components/StructuredData";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Audio Wave Generator - Professional Waveform Video Creator",
  description: "Transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes.",
  keywords: ["audio wave generator", "wave form generator", "audio visualization", "waveform", "video generator", "music visualization", "audio to video", "sound visualization", "audio waveform", "sound wave generator"],
  authors: [{ name: "Audio Wave Generator" }],
  metadataBase: new URL('https://www.audiowavegenerator.com'),
  openGraph: {
    title: "Audio Wave Generator - Professional Waveform Video Creator",
    description: "Transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes.",
    url: 'https://www.audiowavegenerator.com',
    siteName: 'Audio Wave Generator',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Audio Wave Generator - Professional Waveform Video Creator",
    description: "Transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <StructuredData />
        {children}
        <Analytics />
      </body>
    </html>
  );
}
